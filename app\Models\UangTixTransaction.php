<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UangTixTransaction extends Model
{
    use HasFactory;

    protected $table = 'uangtix_transactions';

    protected $fillable = [
        'transaction_number',
        'user_id',
        'type',
        'amount',
        'balance_before',
        'balance_after',
        'status',
        'description',
        'metadata',
        'order_id',
        'event_id',
        'admin_id',
        'from_user_id',
        'to_user_id',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'balance_before' => 'decimal:2',
        'balance_after' => 'decimal:2',
        'metadata' => 'array',
    ];

    // Transaction types
    const TYPE_DEPOSIT = 'deposit';
    const TYPE_WITHDRAWAL = 'withdrawal';
    const TYPE_PURCHASE = 'purchase';
    const TYPE_REFUND = 'refund';
    const TYPE_ADMIN_ADD = 'admin_add';
    const TYPE_ADMIN_DEDUCT = 'admin_deduct';
    const TYPE_TRANSFER_IN = 'transfer_in';
    const TYPE_TRANSFER_OUT = 'transfer_out';

    // Transaction statuses
    const STATUS_PENDING = 'pending';
    const STATUS_COMPLETED = 'completed';
    const STATUS_FAILED = 'failed';
    const STATUS_CANCELLED = 'cancelled';

    /**
     * User relationship
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Order relationship
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Event relationship
     */
    public function event(): BelongsTo
    {
        return $this->belongsTo(Event::class);
    }

    /**
     * Admin relationship
     */
    public function admin(): BelongsTo
    {
        return $this->belongsTo(User::class, 'admin_id');
    }

    /**
     * From user relationship (for transfers)
     */
    public function fromUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'from_user_id');
    }

    /**
     * To user relationship (for transfers)
     */
    public function toUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'to_user_id');
    }

    /**
     * Check if transaction is completed
     */
    public function isCompleted(): bool
    {
        return $this->status === self::STATUS_COMPLETED;
    }

    /**
     * Check if transaction is pending
     */
    public function isPending(): bool
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * Check if transaction is income (positive amount)
     */
    public function isIncome(): bool
    {
        return $this->amount > 0;
    }

    /**
     * Check if transaction is expense (negative amount)
     */
    public function isExpense(): bool
    {
        return $this->amount < 0;
    }

    /**
     * Get formatted amount
     */
    public function getFormattedAmountAttribute(): string
    {
        $prefix = $this->amount >= 0 ? '+' : '';
        return $prefix . number_format(abs($this->amount), 0, ',', '.') . ' UTX';
    }

    /**
     * Get amount in IDR
     */
    public function getAmountInIdrAttribute(): float
    {
        $exchangeRate = UangTixExchangeRate::first();
        return abs($this->amount) * ($exchangeRate->rate_uangtix_to_idr ?? 1);
    }

    /**
     * Get formatted amount in IDR
     */
    public function getFormattedAmountIdrAttribute(): string
    {
        $prefix = $this->amount >= 0 ? '+' : '';
        return $prefix . 'Rp ' . number_format($this->amount_in_idr, 0, ',', '.');
    }

    /**
     * Get transaction type label
     */
    public function getTypeLabelAttribute(): string
    {
        return match($this->type) {
            self::TYPE_DEPOSIT => 'Deposit',
            self::TYPE_WITHDRAWAL => 'Penarikan',
            self::TYPE_PURCHASE => 'Pembelian',
            self::TYPE_REFUND => 'Refund',
            self::TYPE_ADMIN_ADD => 'Penambahan Admin',
            self::TYPE_ADMIN_DEDUCT => 'Pengurangan Admin',
            self::TYPE_TRANSFER_IN => 'Transfer Masuk',
            self::TYPE_TRANSFER_OUT => 'Transfer Keluar',
            default => 'Transaksi'
        };
    }

    /**
     * Get status label
     */
    public function getStatusLabelAttribute(): string
    {
        return match($this->status) {
            self::STATUS_PENDING => 'Menunggu',
            self::STATUS_COMPLETED => 'Selesai',
            self::STATUS_FAILED => 'Gagal',
            self::STATUS_CANCELLED => 'Dibatalkan',
            default => 'Unknown'
        };
    }

    /**
     * Get status color for UI
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            self::STATUS_PENDING => 'warning',
            self::STATUS_COMPLETED => 'success',
            self::STATUS_FAILED => 'danger',
            self::STATUS_CANCELLED => 'secondary',
            default => 'secondary'
        };
    }

    /**
     * Get type color for UI
     */
    public function getTypeColorAttribute(): string
    {
        return match($this->type) {
            self::TYPE_DEPOSIT, self::TYPE_REFUND, self::TYPE_TRANSFER_IN, self::TYPE_ADMIN_ADD => 'success',
            self::TYPE_WITHDRAWAL, self::TYPE_PURCHASE, self::TYPE_TRANSFER_OUT, self::TYPE_ADMIN_DEDUCT => 'danger',
            default => 'primary'
        };
    }

    /**
     * Get type icon for UI
     */
    public function getTypeIconAttribute(): string
    {
        return match($this->type) {
            self::TYPE_DEPOSIT => 'fas fa-plus-circle',
            self::TYPE_WITHDRAWAL => 'fas fa-minus-circle',
            self::TYPE_PURCHASE => 'fas fa-shopping-cart',
            self::TYPE_REFUND => 'fas fa-undo',
            self::TYPE_ADMIN_ADD => 'fas fa-user-plus',
            self::TYPE_ADMIN_DEDUCT => 'fas fa-user-minus',
            self::TYPE_TRANSFER_IN => 'fas fa-arrow-down',
            self::TYPE_TRANSFER_OUT => 'fas fa-arrow-up',
            default => 'fas fa-exchange-alt'
        };
    }

    /**
     * Scope for completed transactions
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', self::STATUS_COMPLETED);
    }

    /**
     * Scope for pending transactions
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * Scope for income transactions
     */
    public function scopeIncome($query)
    {
        return $query->where('amount', '>', 0);
    }

    /**
     * Scope for expense transactions
     */
    public function scopeExpense($query)
    {
        return $query->where('amount', '<', 0);
    }

    /**
     * Scope for specific type
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope for date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Scope for this month
     */
    public function scopeThisMonth($query)
    {
        return $query->whereMonth('created_at', now()->month)
                    ->whereYear('created_at', now()->year);
    }

    /**
     * Scope for today
     */
    public function scopeToday($query)
    {
        return $query->whereDate('created_at', today());
    }
}
