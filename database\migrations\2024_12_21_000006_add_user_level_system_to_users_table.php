<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // User level system
            $table->enum('user_level', ['star', 'star_plus', 'premium', 'platinum'])
                  ->default('star')
                  ->after('role')
                  ->comment('User level: star, star_plus, premium, platinum');
            
            // Level metadata
            $table->timestamp('level_upgraded_at')->nullable()->after('user_level');
            $table->timestamp('level_expires_at')->nullable()->after('level_upgraded_at');
            $table->json('level_benefits')->nullable()->after('level_expires_at');
            $table->text('level_notes')->nullable()->after('level_benefits');
            
            // Level statistics
            $table->integer('total_events_created')->default(0)->after('level_notes');
            $table->integer('total_tickets_sold')->default(0)->after('total_events_created');
            $table->decimal('total_revenue', 15, 2)->default(0)->after('total_tickets_sold');
            $table->decimal('level_score', 8, 2)->default(0)->after('total_revenue');
            
            // Add indexes for performance
            $table->index('user_level');
            $table->index(['role', 'user_level']);
            $table->index('level_expires_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex(['user_level']);
            $table->dropIndex(['role', 'user_level']);
            $table->dropIndex(['level_expires_at']);
            
            $table->dropColumn([
                'user_level',
                'level_upgraded_at',
                'level_expires_at',
                'level_benefits',
                'level_notes',
                'total_events_created',
                'total_tickets_sold',
                'total_revenue',
                'level_score'
            ]);
        });
    }
};
