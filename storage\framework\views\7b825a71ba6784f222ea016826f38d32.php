

<?php $__env->startSection('content'); ?>
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="max-w-3xl mx-auto">
        <!-- Profile Header -->
        <div class="bg-white rounded-xl shadow-sm overflow-hidden mb-6" data-aos="fade-up">
            <div class="bg-primary h-32"></div>
            <div class="px-6 pb-6">
                <div class="flex flex-col items-center -mt-16">
                    <div class="relative">
                        <img src="<?php echo e(auth()->user()->profile_photo_url); ?>"
                             alt="<?php echo e(auth()->user()->name); ?>"
                             class="w-32 h-32 rounded-full border-4 border-white bg-white object-cover">
                        <button onclick="document.getElementById('profile_photo').click()"
                                class="absolute bottom-0 right-0 bg-white rounded-full p-2 shadow-md hover:bg-gray-50">
                            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"/>
                            </svg>
                        </button>
                        <form id="photo-form" action="<?php echo e(route('profile.photo')); ?>" method="POST" enctype="multipart/form-data" class="hidden">
                            <?php echo csrf_field(); ?>
                            <input type="file"
                                   id="profile_photo"
                                   name="profile_photo"
                                   class="hidden"
                                   accept="image/*"
                                   onchange="document.getElementById('photo-form').submit()">
                        </form>
                    </div>
                    <h1 class="text-2xl font-bold mt-4"><?php echo e(auth()->user()->name); ?></h1>
                    <p class="text-gray-600"><?php echo e(auth()->user()->email); ?></p>
                    <div class="flex items-center justify-center space-x-2 mt-2">
                        <span class="px-3 py-1 bg-primary/10 text-primary rounded-full text-sm">
                            <?php echo e(ucfirst(auth()->user()->role)); ?>

                        </span>
                        <?php if(auth()->user()->isPenjual() || auth()->user()->isAdmin()): ?>
                            <?php if (isset($component)) { $__componentOriginal63e2551db4476aff4bab3f62ef8c0aff = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal63e2551db4476aff4bab3f62ef8c0aff = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.user-level-badge','data' => ['user' => auth()->user(),'size' => 'sm']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('user-level-badge'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['user' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(auth()->user()),'size' => 'sm']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal63e2551db4476aff4bab3f62ef8c0aff)): ?>
<?php $attributes = $__attributesOriginal63e2551db4476aff4bab3f62ef8c0aff; ?>
<?php unset($__attributesOriginal63e2551db4476aff4bab3f62ef8c0aff); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal63e2551db4476aff4bab3f62ef8c0aff)): ?>
<?php $component = $__componentOriginal63e2551db4476aff4bab3f62ef8c0aff; ?>
<?php unset($__componentOriginal63e2551db4476aff4bab3f62ef8c0aff); ?>
<?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Form -->
        <div class="bg-white rounded-xl shadow-sm overflow-hidden" data-aos="fade-up" data-aos-delay="100">
            <div class="p-6">
                <h2 class="text-lg font-semibold mb-6">Informasi Profil</h2>
                <form action="<?php echo e(route('profile.update')); ?>" method="POST" class="space-y-6">
                    <?php echo csrf_field(); ?>
                    <!-- Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Nama Lengkap</label>
                        <input type="text"
                               id="name"
                               name="name"
                               value="<?php echo e(old('name', auth()->user()->name)); ?>"
                               class="w-full border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
                               required>
                        <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Email -->
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                        <input type="email"
                               id="email"
                               name="email"
                               value="<?php echo e(old('email', auth()->user()->email)); ?>"
                               class="w-full border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
                               required>
                        <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Phone -->
                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Nomor Telepon</label>
                        <input type="tel"
                               id="phone"
                               name="phone"
                               value="<?php echo e(old('phone', auth()->user()->phone)); ?>"
                               class="w-full border-gray-300 rounded-lg focus:ring-primary focus:border-primary">
                        <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Birth Date -->
                    <div>
                        <label for="birth_date" class="block text-sm font-medium text-gray-700 mb-2">Tanggal Lahir</label>
                        <input type="date"
                               id="birth_date"
                               name="birth_date"
                               value="<?php echo e(old('birth_date', auth()->user()->birth_date ? auth()->user()->birth_date->format('Y-m-d') : '')); ?>"
                               class="w-full border-gray-300 rounded-lg focus:ring-primary focus:border-primary">
                        <?php $__errorArgs = ['birth_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Gender -->
                    <div>
                        <label for="gender" class="block text-sm font-medium text-gray-700 mb-2">Jenis Kelamin</label>
                        <select id="gender"
                                name="gender"
                                class="w-full border-gray-300 rounded-lg focus:ring-primary focus:border-primary">
                            <option value="">Pilih Jenis Kelamin</option>
                            <option value="male" <?php echo e(old('gender', auth()->user()->gender) == 'male' ? 'selected' : ''); ?>>Laki-laki</option>
                            <option value="female" <?php echo e(old('gender', auth()->user()->gender) == 'female' ? 'selected' : ''); ?>>Perempuan</option>
                            <option value="other" <?php echo e(old('gender', auth()->user()->gender) == 'other' ? 'selected' : ''); ?>>Lainnya</option>
                        </select>
                        <?php $__errorArgs = ['gender'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Address -->
                    <div>
                        <label for="address" class="block text-sm font-medium text-gray-700 mb-2">Alamat</label>
                        <textarea id="address"
                                  name="address"
                                  rows="3"
                                  class="w-full border-gray-300 rounded-lg focus:ring-primary focus:border-primary"><?php echo e(old('address', auth()->user()->address)); ?></textarea>
                        <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <!-- Submit Button -->
                    <div class="flex justify-end">
                        <button type="submit"
                                class="bg-primary text-white px-6 py-2 rounded-lg font-medium hover:bg-primary/90 transition-colors">
                            Simpan Perubahan
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Password Change Form -->
        <div class="bg-white rounded-xl shadow-sm overflow-hidden mt-6" data-aos="fade-up" data-aos-delay="150">
            <div class="p-6">
                <h2 class="text-lg font-semibold mb-6">Ubah Password</h2>
                <form action="<?php echo e(route('profile.password')); ?>" method="POST" class="space-y-6">
                    <?php echo csrf_field(); ?>

                    <!-- Current Password -->
                    <div>
                        <label for="current_password" class="block text-sm font-medium text-gray-700 mb-2">Password Saat Ini</label>
                        <input type="password"
                               id="current_password"
                               name="current_password"
                               class="w-full border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
                               required>
                        <?php $__errorArgs = ['current_password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">Password Baru</label>
                            <input type="password"
                                   id="password"
                                   name="password"
                                   class="w-full border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
                                   required>
                            <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div>
                            <label for="password_confirmation" class="block text-sm font-medium text-gray-700 mb-2">Konfirmasi Password</label>
                            <input type="password"
                                   id="password_confirmation"
                                   name="password_confirmation"
                                   class="w-full border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
                                   required>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="flex justify-end">
                        <button type="submit"
                                class="bg-primary text-white px-6 py-2 rounded-lg font-medium hover:bg-primary/90 transition-colors">
                            Ubah Password
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Notification Settings -->
        <div class="bg-white rounded-xl shadow-sm overflow-hidden mt-6" data-aos="fade-up" data-aos-delay="200">
            <div class="p-6">
                <h2 class="text-lg font-semibold mb-6">Pengaturan Notifikasi</h2>
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="font-medium">Email Notifikasi</h3>
                            <p class="text-sm text-gray-600">Terima notifikasi event melalui email</p>
                        </div>
                        <button onclick="toggleNotification('email')"
                                id="email-toggle"
                                class="relative inline-flex items-center h-6 rounded-full w-11 transition-colors
                                       <?php echo e($emailNotification ? 'bg-primary' : 'bg-gray-200'); ?>">
                            <span class="sr-only">Toggle email notification</span>
                            <span class="inline-block w-4 h-4 transform transition-transform bg-white rounded-full
                                       <?php echo e($emailNotification ? 'translate-x-6' : 'translate-x-1'); ?>"></span>
                        </button>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="font-medium">Push Notifikasi</h3>
                            <p class="text-sm text-gray-600">Terima notifikasi langsung di browser</p>
                        </div>
                        <button onclick="toggleNotification('push')"
                                id="push-toggle"
                                class="relative inline-flex items-center h-6 rounded-full w-11 transition-colors
                                       <?php echo e($pushNotification ? 'bg-primary' : 'bg-gray-200'); ?>">
                            <span class="sr-only">Toggle push notification</span>
                            <span class="inline-block w-4 h-4 transform transition-transform bg-white rounded-full
                                       <?php echo e($pushNotification ? 'translate-x-6' : 'translate-x-1'); ?>"></span>
                        </button>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="font-medium">SMS Notifikasi</h3>
                            <p class="text-sm text-gray-600">Terima notifikasi melalui SMS</p>
                        </div>
                        <button onclick="toggleNotification('sms')"
                                id="sms-toggle"
                                class="relative inline-flex items-center h-6 rounded-full w-11 transition-colors
                                       <?php echo e($smsNotification ? 'bg-primary' : 'bg-gray-200'); ?>">
                            <span class="sr-only">Toggle SMS notification</span>
                            <span class="inline-block w-4 h-4 transform transition-transform bg-white rounded-full
                                       <?php echo e($smsNotification ? 'translate-x-6' : 'translate-x-1'); ?>"></span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
async function toggleNotification(type) {
    const toggle = document.getElementById(`${type}-toggle`);
    const toggleSpan = toggle.querySelector('span:last-child');

    try {
        const response = await fetch(`<?php echo e(url('/profile/notifications')); ?>/${type}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
            }
        });

        const data = await response.json();

        if (data.success) {
            // Update toggle appearance
            const isEnabled = data[`${type}Notification`];

            if (isEnabled) {
                toggle.classList.remove('bg-gray-200');
                toggle.classList.add('bg-primary');
                toggleSpan.classList.remove('translate-x-1');
                toggleSpan.classList.add('translate-x-6');
            } else {
                toggle.classList.remove('bg-primary');
                toggle.classList.add('bg-gray-200');
                toggleSpan.classList.remove('translate-x-6');
                toggleSpan.classList.add('translate-x-1');
            }

            // Show success message
            if (window.showNotification) {
                window.showNotification(data.message, 'success');
            }
        }
    } catch (error) {
        console.error('Error toggling notification:', error);
        if (window.showNotification) {
            window.showNotification('Terjadi kesalahan saat mengubah pengaturan notifikasi', 'error');
        }
    }
}
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\Project-tikpro.my.id\resources\views/pages/profile.blade.php ENDPATH**/ ?>