<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UangTixRequest extends Model
{
    use HasFactory;

    protected $table = 'uangtix_requests';

    protected $fillable = [
        'request_number',
        'user_id',
        'type',
        'amount_idr',
        'amount_uangtix',
        'fee_amount',
        'final_amount',
        'status',
        'payment_method',
        'payment_data',
        'payment_reference',
        'bank_name',
        'bank_account_number',
        'bank_account_name',
        'processed_by',
        'processed_at',
        'admin_notes',
        'rejection_reason',
    ];

    protected $casts = [
        'amount_idr' => 'decimal:2',
        'amount_uangtix' => 'decimal:2',
        'fee_amount' => 'decimal:2',
        'final_amount' => 'decimal:2',
        'payment_data' => 'array',
        'processed_at' => 'datetime',
    ];

    // Request types
    const TYPE_DEPOSIT = 'deposit';
    const TYPE_WITHDRAWAL = 'withdrawal';

    // Request statuses
    const STATUS_PENDING = 'pending';
    const STATUS_APPROVED = 'approved';
    const STATUS_REJECTED = 'rejected';
    const STATUS_COMPLETED = 'completed';
    const STATUS_FAILED = 'failed';

    /**
     * User relationship
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Processed by admin relationship
     */
    public function processedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'processed_by');
    }

    /**
     * Check if request is pending
     */
    public function isPending(): bool
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * Check if request is approved
     */
    public function isApproved(): bool
    {
        return $this->status === self::STATUS_APPROVED;
    }

    /**
     * Check if request is completed
     */
    public function isCompleted(): bool
    {
        return $this->status === self::STATUS_COMPLETED;
    }

    /**
     * Check if request is deposit
     */
    public function isDeposit(): bool
    {
        return $this->type === self::TYPE_DEPOSIT;
    }

    /**
     * Check if request is withdrawal
     */
    public function isWithdrawal(): bool
    {
        return $this->type === self::TYPE_WITHDRAWAL;
    }

    /**
     * Approve request
     */
    public function approve(User $admin, string $notes = null): bool
    {
        if (!$this->isPending()) {
            return false;
        }

        $this->update([
            'status' => self::STATUS_APPROVED,
            'processed_by' => $admin->id,
            'processed_at' => now(),
            'admin_notes' => $notes,
        ]);

        // Process the request
        return $this->process();
    }

    /**
     * Reject request
     */
    public function reject(User $admin, string $reason): bool
    {
        if (!$this->isPending()) {
            return false;
        }

        $this->update([
            'status' => self::STATUS_REJECTED,
            'processed_by' => $admin->id,
            'processed_at' => now(),
            'rejection_reason' => $reason,
        ]);

        // Create notification
        \App\Models\Notification::create([
            'user_id' => $this->user_id,
            'title' => $this->isDeposit() ? 'Deposit UangTix Ditolak' : 'Penarikan UangTix Ditolak',
            'message' => "Permintaan {$this->type_label} sebesar {$this->formatted_amount} ditolak. Alasan: {$reason}",
            'type' => 'uangtix',
            'data' => [
                'request_id' => $this->id,
                'type' => $this->type,
                'amount' => $this->final_amount,
                'reason' => $reason,
            ]
        ]);

        return true;
    }

    /**
     * Process approved request
     */
    public function process(): bool
    {
        try {
            $userBalance = $this->user->getUangTixBalance();

            if ($this->isDeposit()) {
                // Add UangTix to user balance
                $userBalance->addBalance($this->final_amount, 'deposit', [
                    'request_id' => $this->id,
                    'amount_idr' => $this->amount_idr,
                    'fee_amount' => $this->fee_amount,
                ]);

                $message = "Deposit UangTix sebesar {$this->formatted_amount} berhasil diproses.";
            } else {
                // Deduct UangTix from user balance
                $userBalance->deductBalance($this->amount_uangtix, 'withdrawal', [
                    'request_id' => $this->id,
                    'amount_idr' => $this->final_amount,
                    'fee_amount' => $this->fee_amount,
                    'bank_info' => [
                        'bank_name' => $this->bank_name,
                        'account_number' => $this->bank_account_number,
                        'account_name' => $this->bank_account_name,
                    ],
                ]);

                $message = "Penarikan UangTix sebesar {$this->formatted_amount} berhasil diproses.";
            }

            $this->update(['status' => self::STATUS_COMPLETED]);

            // Create notification
            \App\Models\Notification::create([
                'user_id' => $this->user_id,
                'title' => $this->isDeposit() ? 'Deposit UangTix Berhasil' : 'Penarikan UangTix Berhasil',
                'message' => $message,
                'type' => 'uangtix',
                'data' => [
                    'request_id' => $this->id,
                    'type' => $this->type,
                    'amount' => $this->final_amount,
                ]
            ]);

            return true;

        } catch (\Exception $e) {
            $this->update(['status' => self::STATUS_FAILED]);

            // Create error notification
            \App\Models\Notification::create([
                'user_id' => $this->user_id,
                'title' => 'Gagal Memproses Permintaan UangTix',
                'message' => "Terjadi kesalahan saat memproses {$this->type_label}. Silakan hubungi customer service.",
                'type' => 'uangtix',
                'data' => [
                    'request_id' => $this->id,
                    'type' => $this->type,
                    'error' => $e->getMessage(),
                ]
            ]);

            return false;
        }
    }

    /**
     * Get formatted amount
     */
    public function getFormattedAmountAttribute(): string
    {
        if ($this->isDeposit()) {
            return number_format($this->final_amount, 0, ',', '.') . ' UTX';
        } else {
            return 'Rp ' . number_format($this->final_amount, 0, ',', '.');
        }
    }

    /**
     * Get type label
     */
    public function getTypeLabelAttribute(): string
    {
        return match($this->type) {
            self::TYPE_DEPOSIT => 'Deposit',
            self::TYPE_WITHDRAWAL => 'Penarikan',
            default => 'Unknown'
        };
    }

    /**
     * Get status label
     */
    public function getStatusLabelAttribute(): string
    {
        return match($this->status) {
            self::STATUS_PENDING => 'Menunggu',
            self::STATUS_APPROVED => 'Disetujui',
            self::STATUS_REJECTED => 'Ditolak',
            self::STATUS_COMPLETED => 'Selesai',
            self::STATUS_FAILED => 'Gagal',
            default => 'Unknown'
        };
    }

    /**
     * Get status color for UI
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            self::STATUS_PENDING => 'warning',
            self::STATUS_APPROVED => 'info',
            self::STATUS_COMPLETED => 'success',
            self::STATUS_REJECTED, self::STATUS_FAILED => 'danger',
            default => 'secondary'
        ];
    }

    /**
     * Generate unique request number
     */
    public static function generateRequestNumber(string $type): string
    {
        $prefix = $type === self::TYPE_DEPOSIT ? 'UTX-DEP' : 'UTX-WD';
        return $prefix . '-' . strtoupper(uniqid());
    }

    /**
     * Scope for pending requests
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * Scope for deposits
     */
    public function scopeDeposits($query)
    {
        return $query->where('type', self::TYPE_DEPOSIT);
    }

    /**
     * Scope for withdrawals
     */
    public function scopeWithdrawals($query)
    {
        return $query->where('type', self::TYPE_WITHDRAWAL);
    }

    /**
     * Scope for today
     */
    public function scopeToday($query)
    {
        return $query->whereDate('created_at', today());
    }
}
